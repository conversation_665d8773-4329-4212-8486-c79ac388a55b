<?php include '../components/authentication.php';?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <?php include '../components/header.php';
        include '../db_conn.php';
        ?>

        <!-- DataTables CSS -->
        <link rel="stylesheet" media="screen, print" href="../css/datagrid/datatables/datatables.bundle.css">
        
    </head>

    <body class="desktop chrome webkit nav-function-top mod-main-boxed mod-pace-custom mod-panel-icon nav-mobile-push nav-function-fixed header-function-fixed mod-clean-page-bg pace-done mod-bg-3 mod-fixed-bg " >
        <!-- DOC: script to save and load page settings -->
        <script src="../components/top-script.js"></script>
        <!-- BEGIN Page Wrapper -->
        <div class="page-wrapper">
            <div class="page-inner">
                <!-- BEGIN Left Aside -->
                <aside class="page-sidebar">
                    <div class="page-logo">
                        <a href="#" class="page-logo-link press-scale-down d-flex align-items-center position-relative" data-toggle="modal" data-target="#modal-shortcut">
                            <img src="../img/logo.png" alt="SmartAdmin WebApp" aria-roledescription="logo">
                            <span class="page-logo-text mr-1">SmartAdmin WebApp</span>
                            <span class="position-absolute text-white opacity-50 small pos-top pos-right mr-2 mt-n2"></span>
                            <i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>
                        </a>
                    </div>
                    <!-- BEGIN PRIMARY NAVIGATION -->
                   <?php include '../components/primary-nav-admin-inventory.php';?>
                    <!-- END PRIMARY NAVIGATION -->
                   
                </aside>
                <!-- END Left Aside -->
                <div class="page-content-wrapper">
                    <!-- BEGIN Page Header -->
                    <?php include '../components/page-header.php';?>
                    <!-- END Page Header -->
                    <!-- BEGIN Page Content -->
                    <!-- the #js-page-content id is needed for some plugins to initialize -->
                     <?php
                      $store_id = isset($_GET['storeID']) ? intval($_GET['storeID']) : 0;
                      $store_name = '';
                      if ($store_id > 0) {
                        $store_sql = "SELECT client FROM store_info WHERE id = ?";
                        $store_stmt = $conn->prepare($store_sql);
                        $store_stmt->bind_param('i', $store_id);
                        $store_stmt->execute();
                        $store_result = $store_stmt->get_result();
                        if ($store_result->num_rows > 0) {
                            $store_row = $store_result->fetch_assoc();
                            $store_name = $store_row['client'];
                        }
                      }
                    ?>
                    <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);">Inventory Management</a></li>
                            <li class="breadcrumb-item font-weight-bold"><?= $store_name; ?></li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"> <?= date ?> </li>
                        </ol>
                    
                        <!-- Your main content goes below here: -->
												 <div class="row">
                            <div class="col-xl-12">
                                <div id="panel-content" class="panel">
																	<div class="p-4">
																			<!-- Action buttons -->
																			
                                     <!-- datatable start -->
                                    <table id="dt-products" class="table table-bordered table-hover table-sm table-striped w-100 ">
                                        <thead class="bg-dark text-light">
                                            <tr>
                                                <!-- <th style="font-size:12px; width: 30px;"></th> -->
																								<th style="font-size:12px;">Barcode</th>
                                                <th style="font-size:12px;">Category</th>
																								<th style="font-size:12px; width: 100px;">Image</th>
                                                <th style="font-size:12px;">Product name</th>
                                                <th style="font-size:12px;">Product description</th>
                                                <th style="font-size:12px;">Purchasable Qty</th>
                                                <th style="font-size:12px;">Sold</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
																				<!-- datatable end -->
																	</div>
															</div>
														</div>
													</div>
                        <?php include 'modal.php';?>
                    </main>
                    <!-- this overlay is activated only when mobile menu is triggered -->
                    <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div> <!-- END Page Content -->
                    <!-- BEGIN Page Footer -->
                    <?php include '../components/footer.php';?>
                    
                </div>
            </div>
        </div>

       <?php include '../components/bottom.php';?>

       <!-- DataTables JavaScript -->
       <script src="../js/datagrid/datatables/datatables.bundle.js"></script>

       <script>
           $(document).ready(function() {
               // Handle Add Product button click with conditional modal logic
               $('#btn-add-product').on('click', function() {
                   // Check if Add Category modal is currently visible
                   if ($('#modal-add-category').hasClass('show')) {
                       // Hide Add Category modal first, then show Add Product modal
                       $('#modal-add-category').modal('hide');

                       // Wait for the category modal to finish hiding before showing product modal
                       $('#modal-add-category').on('hidden.bs.modal.addProduct', function() {
                           // Remove this specific event listener to avoid multiple bindings
                           $(this).off('hidden.bs.modal.addProduct');
                           // Show Add Product modal
                           $('#modal-add-product').modal('show');
                       });
                   } else {
                       // Add Category modal is not visible, simply show Add Product modal
                       $('#modal-add-product').modal('show');
                   }
               });

               // Handle Add Category button click with conditional modal logic
               $('#btn-add-category').on('click', function() {
                   // Check if Add Product modal is currently visible
                   if ($('#modal-add-product').hasClass('show')) {
                       // Hide Add Product modal first, then show Add Category modal
                       $('#modal-add-product').modal('hide');

                       // Wait for the product modal to finish hiding before showing category modal
                       $('#modal-add-product').on('hidden.bs.modal.addCategory', function() {
                           // Remove this specific event listener to avoid multiple bindings
                           $(this).off('hidden.bs.modal.addCategory');
                           // Show Add Category modal
                           $('#modal-add-category').modal('show');
                       });
                   } else {
                       // Add Product modal is not visible, simply show Add Category modal
                       $('#modal-add-category').modal('show');
                   }
               });

               // Handle Back to Product button click
               $('#btn-back-to-product').on('click', function() {
                   // Hide Add Category modal first, then show Add Product modal
                   $('#modal-add-category').modal('hide');

                   // Wait for the category modal to finish hiding before showing product modal
                   $('#modal-add-category').on('hidden.bs.modal.backToProduct', function() {
                       // Remove this specific event listener to avoid multiple bindings
                       $(this).off('hidden.bs.modal.backToProduct');
                       // Show Add Product modal
                       $('#modal-add-product').modal('show');
                   });
               });

               // Handle Add Category form submission via AJAX
               $('#form-add-category').on('submit', function(e) {
                   e.preventDefault(); // Prevent default form submission

                   // Get the submit button
                   var $submitBtn = $('#btnAddCategory');
                   var originalBtnText = $submitBtn.html();

                   // Disable submit button and show loading state
                   $submitBtn.prop('disabled', true).html('<i class="fal fa-spinner fa-spin"></i> Adding...');

                   // Create FormData object
                   var formData = new FormData(this);

                   // Add store ID from URL parameter if present
                   const urlParams = new URLSearchParams(window.location.search);
                   if (urlParams.get('storeID')) {
                       formData.append('storeID', urlParams.get('storeID'));
                   }

                   // Submit via AJAX
                   $.ajax({
                       url: 'add_category.php',
                       type: 'POST',
                       data: formData,
                       processData: false,
                       contentType: false,
                       dataType: 'json',
                       success: function(response) {
                           // Re-enable submit button
                           $submitBtn.prop('disabled', false).html(originalBtnText);

                           if (response.status === 'success') {
                               // Show success notification
                               toastr.success(response.message, 'Success');

                               // Clear form fields
                               $('#form-add-category')[0].reset();

                               // Refresh category dropdown
                               refreshCategoryDropdown(response.category_id, response.category_name);

                               // Automatically return to Add Product modal
                               $('#modal-add-category').modal('hide');

                               // Wait for category modal to hide before showing product modal
                               $('#modal-add-category').on('hidden.bs.modal.categoryAdded', function() {
                                   $(this).off('hidden.bs.modal.categoryAdded');
                                   $('#modal-add-product').modal('show');
                               });

                           } else {
                               // Show error notification
                               toastr.error(response.message || 'Failed to add category', 'Error');
                           }
                       },
                       error: function(xhr, status, error) {
                           // Re-enable submit button
                           $submitBtn.prop('disabled', false).html(originalBtnText);

                           // Try to parse error response
                           var errorMessage = 'An error occurred while adding the category';

                           try {
                               var response = JSON.parse(xhr.responseText);
                               if (response.message) {
                                   errorMessage = response.message;
                               }
                           } catch (e) {
                               console.error('Error parsing response:', e);
                           }

                           // Show error notification
                           toastr.error(errorMessage, 'Error');

                           // Log error for debugging
                           console.error('AJAX Error:', {
                               status: status,
                               error: error,
                               response: xhr.responseText
                           });
                       }
                   });
               });

               // Function to refresh category dropdown
               function refreshCategoryDropdown(newCategoryId, newCategoryName) {
                   const urlParams = new URLSearchParams(window.location.search);
                   const storeID = urlParams.get('storeID');

                   if (!storeID) {
                       console.error('Store ID not found in URL parameters');
                       return;
                   }

                   // Fetch updated categories from server
                   $.ajax({
                       url: 'ajax/get_categories.php',
                       type: 'POST',
                       data: { storeID: storeID },
                       dataType: 'json',
                       success: function(categories) {
                           // Clear existing options except the first one
                           $('#product_category').find('option:not(:first)').remove();

                           // Add updated categories
                           $.each(categories, function(index, category) {
                               $('#product_category').append(
                                   $('<option></option>').val(category.id).text(category.category)
                               );
                           });

                           // Select the newly added category
                           if (newCategoryId) {
                               $('#product_category').val(newCategoryId);
                           }
                       },
                       error: function(xhr, status, error) {
                           console.error('Failed to refresh categories:', error);
                           // Fallback: manually add the new category
                           if (newCategoryId && newCategoryName) {
                               $('#product_category').append(
                                   $('<option></option>').val(newCategoryId).text(newCategoryName)
                               );
                               $('#product_category').val(newCategoryId);
                           }
                       }
                   });
               }

               // Initialize DataTables with AJAX
               $('#dt-products').DataTable({
                   processing: true,
                   serverSide: true,
                   ajax: {
                       url: 'ajax/get_products.php',
                       type: 'POST',
                       data: function(d) {
                           // Pass URL parameter to DataTables AJAX request
                           const urlParams = new URLSearchParams(window.location.search);
                           if (urlParams.get('storeID')) {
                               d.storeID = urlParams.get('storeID');
                           }
                           return d;
                       },
                       error: function(xhr, error, code) {
                           console.error('DataTables AJAX error:', error);
                           if (window.showToast) {
                               window.showToast('error', 'Failed to load products data. Please try again.');
                           }
                       }
                   },
                   columns: [
									 {
                           title: 'Barcode',
                           data: 0,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
                       {
                           title: 'Category',
                           data: 1,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
											 {
                           title: 'Image',
                           data: 2,
                           orderable: false,
                           searchable: false,
                           className: 'text-center p-2'
                       },
                       {
                           title: 'Product Name',
                           data: 3,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
                       {
                           title: 'Product Description',
                           data: 4,
                           orderable: true,
                           searchable: true,
													 	className: 'p-2'
                       },
                       {
                           title: 'Purchasable Qty',
                           data: 5,
                           orderable: true,
                           searchable: true,
                           className: 'text-center p-2'
                       }
                    //    {
                    //        title: 'Sold',
                    //        data: 6,
                    //        orderable: true,
                    //        searchable: true,
                    //        className: 'text-center p-2'
                    //    }
                   ],
                   order: [[0, 'asc']], // Default sort by category name
                   pageLength: 25,
                   lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                   language: {
                       processing: "Loading products...",
                       emptyTable: "No products found",
                       zeroRecords: "No matching products found"
                   },
                   responsive: true,
                   autoWidth: false,
                   stateSave: true, // Save table state (pagination, sorting, etc.)
                   dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 text-right"f>>' +
                        '<"row"<"col-sm-12"tr>>' +
                        '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
               });

           });

					 //Query product with product code
					 function queryCredentialsAndPopulateModal(rowData) {
        // Extract the product code information
        var productcode = rowData.product_code;

        console.log('Row data:', rowData);
        console.log('Product Code:', productcode);

        // Show loading indicator
        //toastr.info('Loading product credentials...', 'Please wait');

        // Query the product credentials via AJAX
        $.ajax({
            url: 'ajax/fetch_product.php',
            type: 'POST',
            dataType: 'json',
            data: function() {
                // Build data object
                var ajaxData = {
                    action: 'query_product_credentials',
                    productcode: productcode
                };

                // Pass URL parameter to AJAX requests if present
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('storeID')) {
                    ajaxData.storeID = urlParams.get('storeID');
                }

                return ajaxData;
            }(),
            success: function(response) {
                console.log('Raw response:', response);
                console.log('Response type:', typeof response);

                // Check if response is already an object (jQuery auto-parsed)
                if (typeof response === 'object') {
                    console.log('Response is already an object:', response);

                    if (response.success) {
                        // Populate the modal with the product credentials
                        populateLinkSetModal(response.data);

                        // Show the modal
                        $('#modal-add-product').modal('show');

                        //toastr.success('Product credentials loaded on the modal');
                    } else {
                        console.error('Server error:', response.message);
                        toastr.error(response.message || 'Failed to load product credentials');

                        // Show debug info if available
                        if (response.debug) {
                            console.error('Debug info:', response.debug);
                        }
                    }
                } else {
                    // Try to parse as JSON
                    try {
                        const result = JSON.parse(response);
                        console.log('Parsed result:', result);

                        if (result.success) {
                            // Populate the modal with the credentials
                            populateLinkSetModal(result.data);

                            // Show the modal
                            $('#modal-add-product').modal('show');

                            //toastr.success('Product credentials loaded on the modal');
                        } else {
                            console.error('Server error:', result.message);
                            toastr.error(result.message || 'Failed to load product credentials');

                            // Show debug info if available
                            if (result.debug) {
                                console.error('Debug info:', result.debug);
                            }
                        }
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response was:', response);
                        console.error('Response length:', response.length);
                        console.error('First 500 chars:', response.substring(0, 500));
                        toastr.error('Invalid server response. Check console for details.');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                console.error('Response text:', xhr.responseText);
                toastr.error('Server error occurred while loading product credentials: ' + error);
            }
        });
    }

    // Inventory Import Functionality
    $(document).ready(function() {
        // Load categories when modal is shown
        $('#modal-inventory').on('show.bs.modal', function() {
            loadCategoriesForFilter();
        });

        // Function to load categories for the filter dropdown
        function loadCategoriesForFilter() {
            const urlParams = new URLSearchParams(window.location.search);
            const storeID = urlParams.get('storeID');

            if (!storeID) {
                console.error('Store ID not found for loading categories');
                return;
            }

            $.ajax({
                url: 'ajax/get_categories.php',
                type: 'POST',
                data: { storeID: storeID },
                dataType: 'json',
                success: function(categories) {
                    const $categorySelect = $('#template_category_filter');

                    // Clear existing options except the first one
                    $categorySelect.find('option:not(:first)').remove();

                    // Add categories to dropdown
                    $.each(categories, function(index, category) {
                        $categorySelect.append(
                            $('<option></option>').val(category.id).text(category.category)
                        );
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Failed to load categories:', error);
                    if (window.showToast) {
                        window.showToast('warning', 'Failed to load categories for filter');
                    }
                }
            });
        }

        // Handle template download
        $('#btn-download-template').on('click', function(e) {
            e.preventDefault();

            // Get store ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const storeID = urlParams.get('storeID');

            if (!storeID) {
                if (window.showToast) {
                    window.showToast('error', 'Store ID is required to download template');
                } else {
                    alert('Store ID is required to download template');
                }
                return;
            }

            // Get selected category filter
            const categoryFilter = $('#template_category_filter').val();

            // Create download URL (relative to current inventory directory)
            let downloadUrl = `./download_template.php?storeID=${storeID}`;

            // Add category filter if selected
            if (categoryFilter && categoryFilter !== '') {
                downloadUrl += `&categoryID=${categoryFilter}`;
            }

            // Trigger download by navigating to the PHP script
            // This will properly execute the PHP script and trigger the Excel download
            window.location.href = downloadUrl;
        });

        // Handle inventory import form submission
        $('#form-inventory').on('submit', function(e) {
            e.preventDefault();

            const form = this;
            const $form = $(form);
            const $submitBtn = $('#btnAddInventory');
            const $btnText = $submitBtn.find('.btn-text');
            const $btnLoading = $submitBtn.find('.btn-loading');
            const $progressSection = $('#import-progress');
            const $resultsSection = $('#import-results');
            const $resultsContent = $('#results-content');

            // Validate file selection
            const fileInput = document.getElementById('inventory_file');
            if (!fileInput.files.length) {
                if (window.showToast) {
                    window.showToast('error', 'Please select a file to import');
                } else {
                    alert('Please select a file to import');
                }
                return;
            }

            // Get store ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const storeID = urlParams.get('storeID');

            if (!storeID) {
                if (window.showToast) {
                    window.showToast('error', 'Store ID is required for import');
                } else {
                    alert('Store ID is required for import');
                }
                return;
            }

            // Show loading state
            $submitBtn.prop('disabled', true);
            $btnText.hide();
            $btnLoading.show();
            $progressSection.show();
            $resultsSection.hide();

            // Animate progress bar
            const $progressBar = $progressSection.find('.progress-bar');
            $progressBar.css('width', '30%');

            // Create FormData
            const formData = new FormData(form);

            // Submit via AJAX
            $.ajax({
                url: `add_inventory.php?storeID=${storeID}`,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    console.log('Import response:', response);

                    // Complete progress bar
                    $progressBar.css('width', '100%');

                    setTimeout(function() {
                        // Hide progress, show results
                        $progressSection.hide();
                        $resultsSection.show();

                        // Display results
                        displayImportResults(response);

                        // Show success/error toast
                        if (response.status === 'success') {
                            if (window.showToast) {
                                window.showToast('success', response.message);
                            }

                            // Refresh the DataTable to show updated quantities
                            if ($.fn.DataTable.isDataTable('#dt-products')) {
                                $('#dt-products').DataTable().ajax.reload();
                            }
                        } else if (response.status === 'success_with_warnings') {
                            if (window.showToast) {
                                window.showToast('warning', response.message);
                            }

                            // Refresh the DataTable to show updated quantities
                            if ($.fn.DataTable.isDataTable('#dt-products')) {
                                $('#dt-products').DataTable().ajax.reload();
                            }
                        } else if (response.status === 'partial_success') {
                            if (window.showToast) {
                                window.showToast('warning', response.message);
                            }

                            // Refresh the DataTable
                            if ($.fn.DataTable.isDataTable('#dt-products')) {
                                $('#dt-products').DataTable().ajax.reload();
                            }
                        } else if (response.status === 'warning') {
                            if (window.showToast) {
                                window.showToast('warning', response.message);
                            }
                        } else {
                            if (window.showToast) {
                                window.showToast('error', response.message);
                            }
                        }

                        // Reset button state
                        $submitBtn.prop('disabled', false);
                        $btnText.show();
                        $btnLoading.hide();

                    }, 500);
                },
                error: function(xhr, status, error) {
                    console.error('Import error:', status, error);
                    console.error('Response:', xhr.responseText);

                    // Hide progress, show error
                    $progressSection.hide();
                    $resultsSection.show();

                    let errorMessage = 'Import failed due to server error';
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.message || errorMessage;
                        displayImportResults(errorResponse);
                    } catch (e) {
                        $resultsContent.html(`
                            <div class="alert alert-danger">
                                <h6><i class="fal fa-exclamation-triangle"></i> Import Failed</h6>
                                <p>${errorMessage}</p>
                            </div>
                        `);
                    }

                    if (window.showToast) {
                        window.showToast('error', errorMessage);
                    }

                    // Reset button state
                    $submitBtn.prop('disabled', false);
                    $btnText.show();
                    $btnLoading.hide();
                }
            });
        });

        // Function to display import results
        function displayImportResults(response) {
            const data = response.data || {};
            const status = response.status || 'error';

            let alertClass = 'alert-success';
            let icon = 'fa-check-circle';

            if (status === 'error') {
                alertClass = 'alert-danger';
                icon = 'fa-exclamation-triangle';
            } else if (status === 'partial_success') {
                alertClass = 'alert-warning';
                icon = 'fa-exclamation-circle';
            } else if (status === 'success_with_warnings') {
                alertClass = 'alert-warning';
                icon = 'fa-check-circle';
            } else if (status === 'warning') {
                alertClass = 'alert-warning';
                icon = 'fa-exclamation-triangle';
            }

            let html = `
                <div class="alert ${alertClass}">
                    <h6><i class="fal ${icon}"></i> ${response.message}</h6>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>Import Statistics:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Total Rows:</strong> ${data.total_rows || 0}</li>
                            <li><strong>Processed:</strong> ${data.processed || 0}</li>
                            <li><strong>Updated:</strong> ${data.updated || 0}</li>
                            <li><strong>Skipped:</strong> ${data.skipped || 0}</li>
                            <li><strong>Errors:</strong> ${data.error_count || 0}</li>
                            <li><strong>Warnings:</strong> ${data.warning_count || 0}</li>
                            <li><strong>Info Messages:</strong> ${data.info_count || 0}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Actions:</h6>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="$('#modal-inventory').modal('hide');">
                            Close Import
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="resetImportForm();">
                            Import Another File
                        </button>
                    </div>
                </div>
            `;

            // Add error details if there are any
            if (data.errors && data.errors.length > 0) {
                html += `
                    <div class="mt-3">
                        <h6><i class="fal fa-exclamation-triangle text-danger"></i> Errors:</h6>
                        <div class="alert alert-danger border" style="max-height: 200px; overflow-y: auto;">
                            <ul class="mb-0 small">
                `;

                data.errors.forEach(function(error) {
                    html += `<li>${error}</li>`;
                });

                html += `
                            </ul>
                        </div>
                    </div>
                `;
            }

            // Add warning details if there are any
            if (data.warnings && data.warnings.length > 0) {
                html += `
                    <div class="mt-3">
                        <h6><i class="fal fa-exclamation-circle text-warning"></i> Warnings:</h6>
                        <div class="alert alert-warning border" style="max-height: 200px; overflow-y: auto;">
                            <ul class="mb-0 small">
                `;

                data.warnings.forEach(function(warning) {
                    html += `<li>${warning}</li>`;
                });

                html += `
                            </ul>
                        </div>
                    </div>
                `;
            }

            // Add info messages if there are any
            if (data.info_messages && data.info_messages.length > 0) {
                html += `
                    <div class="mt-3">
                        <h6><i class="fal fa-info-circle text-info"></i> Update Details:</h6>
                        <div class="alert alert-info border" style="max-height: 200px; overflow-y: auto;">
                            <ul class="mb-0 small">
                `;

                data.info_messages.forEach(function(info) {
                    html += `<li>${info}</li>`;
                });

                html += `
                            </ul>
                        </div>
                    </div>
                `;
            }

            $('#results-content').html(html);
        }

        // Function to reset import form
        window.resetImportForm = function() {
            $('#form-inventory')[0].reset();
            $('#import-progress').hide();
            $('#import-results').hide();
            $('#btnAddInventory').prop('disabled', false);
            $('#btnAddInventory .btn-text').show();
            $('#btnAddInventory .btn-loading').hide();
        };

        // Reset form when modal is closed
        $('#modal-inventory').on('hidden.bs.modal', function() {
            resetImportForm();
        });
    });
       </script>

			<!-- Enhanced Search Styles -->
			<style>
			.modal-product-item {
			    transition: all 0.2s ease;
			    border-left: 3px solid transparent;
			}

			.modal-product-item:hover {
			    background-color: #f8f9fa;
			    border-left-color: #007bff;
			}

			.modal-product-item.active {
			    background-color: #e7f3ff;
			    border-left-color: #007bff;
			    border-color: #007bff;
			}

			#modalSearchResults {
			    border: 1px solid #dee2e6;
			    border-radius: 0.375rem;
			    background-color: #fff;
			}

			.list-group-item:first-child {
			    border-top-left-radius: 0.375rem;
			    border-top-right-radius: 0.375rem;
			}

			.list-group-item:last-child {
			    border-bottom-left-radius: 0.375rem;
			    border-bottom-right-radius: 0.375rem;
			}

			.btn-populate-form {
			    min-width: 60px;
			}

			#modalProductSearch:focus {
			    border-color: #007bff;
			    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
			}
			</style>

			<script src="../assets/js/zoom_image.js"></script>
    </body>
    <!-- END Body -->
</html>
