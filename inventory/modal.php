<!-- add inventory modal -->
<div class="modal fade" id="modal-inventory" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
			<div class="modal-content">
					<div class="modal-header">
							<h3 class="modal-title font-weight-bold text-dark"><i class="fal fa-circle"></i> I N V E N T O R Y</h3>
							<button type="button" class="close" data-dismiss="modal" aria-label="Close">
									<span aria-hidden="true"><i class="fal fa-times"></i></span>
							</button>
					</div>

					<form id="form-inventory" method="POST" enctype="multipart/form-data" class="p-4">
						<div class="modal-body rounded" style="background-color: #E5F1F5FF;">

							<!-- File Upload Section -->
							<div class="form-group">
								<label for="inventory_file" class="text-dark font-weight-bold">Inventory File <code>*</code></label>
								<input type="file" class="form-control" id="inventory_file" name="inventory_file" accept=".csv,.xlsx,.xls" required>
								<small class="form-text text-muted">
									Supported formats: Excel (.xlsx, .xls) and CSV (.csv). Maximum file size: 10MB.
								</small>
							</div>

							<!-- Instructions -->
							<div class="alert alert-info" role="alert">
								<h6 class="alert-heading"><i class="fal fa-info-circle"></i> Import Instructions:</h6>
								<ul class="mb-0 small">
									<li>Download the template to get your current inventory data</li>
									<li>Update only the <strong>product_qty</strong> column with new quantities</li>
									<li>Products are matched by ID, product code, or product name</li>
									<li>Only existing products will be updated</li>
								</ul>
							</div>

							<!-- Progress Bar (hidden by default) -->
							<div id="import-progress" class="form-group" style="display: none;">
								<label class="text-dark font-weight-bold">Import Progress:</label>
								<div class="progress">
									<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
								</div>
								<small class="text-muted">Processing your file...</small>
							</div>

							<!-- Results Section (hidden by default) -->
							<div id="import-results" class="form-group" style="display: none;">
								<label class="text-dark font-weight-bold">Import Results:</label>
								<div id="results-content" class="border rounded p-3 bg-white">
									<!-- Results will be populated here -->
								</div>
							</div>

							<input type="hidden" name="add_inventory" value="1">
							<input type="hidden" name="store_owner" value="<?php echo $_SESSION['auth_user']['uid']; ?>">
						</div>

						<div class="d-flex align-items-center border rounded p-2 mt-2">
								<!-- Category Filter for Template Download -->
								<div class="form-group mb-0 mr-3">
									<label for="template_category_filter" class="text-dark font-weight-bold mb-1" style="font-size: 12px;">Filter by Category:</label>
									<select id="template_category_filter" class="form-control form-control-sm" style="width: 200px;">
										<option value="">All Categories</option>
										<!-- Categories will be populated via AJAX -->
									</select>
								</div>
								<!-- Download Template Button -->
								<div class="form-group mb-0 mr-3">
									<div class=""><i class="fal fa-hand-point-down"></i></div>
									<button type="button" id="btn-download-template" class="btn btn-sm btn-outline-success">
										<i class="fal fa-file-excel"></i> Download Template
									</button>
								</div>
							</div>

						<div class="modal-footer d-flex">
							<div class="items-right ml-auto">
								<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
								<button type="submit" id="btnAddInventory" class="btn btn-primary">
									<span class="btn-text">Import Inventory</span>
									<span class="btn-loading" style="display: none;">
										<i class="fal fa-spinner fa-spin"></i> Importing...
									</span>
								</button>
							</div>
						</div>
					</form>
			</div>
	</div>
</div>