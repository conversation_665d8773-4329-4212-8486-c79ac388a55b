# Import Logging System

This document describes the comprehensive logging functionality implemented for the import process in the POS system.

## Overview

The logging system provides two levels of logging:

1. **High-level Import Logging** (`logs_import` table) - Records metadata about each import operation
2. **Row-level Audit Logging** (`logs_audit` table) - Records detailed information about each row processed during import

## Database Tables

### logs_import Table
Records one entry per import operation with the following fields:
- `id` - Auto-increment primary key
- `store_id` - Store ID for multi-tenant security
- `log_type` - Type of import (e.g., 'inventory_import')
- `page_title` - Page/module where import occurred
- `file_name` - Name of the imported file
- `log_status` - Import status with statistics (e.g., 'success|total_rows:10|updated:8|errors:2')
- `log_user` - User who performed the import
- `log_datetime` - Timestamp of the import operation

### logs_audit Table
Records one entry per row processed with the following fields:
- `id` - Auto-increment primary key
- `store_id` - Store ID for multi-tenant security
- `log_type` - Type of audit log (e.g., 'inventory_row_processing')
- `page_title` - Page/module where processing occurred
- `column_barcode` - Product barcode being processed
- `column_item` - Product name being processed
- `column_qty` - Quantity and processing status (e.g., '5|success_updated')
- `log_user` - User who performed the import
- `log_datetime` - Timestamp of the row processing

## Implementation

### ImportLogger Class

The `ImportLogger` class in `utils/import_logger.php` provides the following methods:

#### logImportStart()
```php
$import_log_id = $logger->logImportStart(
    $store_id,
    'inventory_import',
    'Inventory Management',
    $file_name,
    $current_user
);
```

#### logImportEnd()
```php
$logger->logImportEnd($import_log_id, $final_status, $stats);
```

#### logAuditRow()
```php
$logger->logAuditRow(
    $store_id,
    'inventory_row_processing',
    'Inventory Management',
    $barcode,
    $product_name,
    $quantity,
    $processing_status,
    $current_user
);
```

### Processing Status Values

The following status values are used in the audit logs:

- `success_updated` - Row processed successfully and product updated
- `failed_missing_barcode` - Row failed due to missing barcode
- `failed_invalid_quantity` - Row failed due to invalid quantity value
- `failed_product_not_found` - Row failed because product was not found
- `failed_update_error` - Row failed due to database update error
- `failed_exception` - Row failed due to unexpected exception
- `skipped_empty_row` - Row was skipped because it was empty

## Usage in Import Process

The logging is integrated into `inventory/add_inventory.php` as follows:

1. **Import Start**: Log is created when import begins
2. **Row Processing**: Each row is logged with its processing outcome
3. **Import End**: Final status and statistics are recorded

## Viewing Logs

### Web Interface
Use `utils/test_import_logs.html` to view logs through a web interface:
- View recent import operations
- View row-level audit trail
- View import statistics

### API Endpoints
Use `utils/view_import_logs.php` with the following actions:
- `action=list_imports` - List recent import operations
- `action=list_audit` - List audit trail entries
- `action=stats` - Get import statistics
- `action=import_details&import_log_id=X` - Get details for specific import

### Example API Calls
```
GET utils/view_import_logs.php?action=list_imports&storeID=1&limit=10
GET utils/view_import_logs.php?action=list_audit&storeID=1&limit=50
GET utils/view_import_logs.php?action=stats&storeID=0
```

## Security Features

- **Multi-tenant Security**: All logs are scoped by `store_id`
- **Authentication Required**: All log viewing requires user authentication
- **Error Handling**: Comprehensive error handling prevents logging failures from breaking imports
- **Transaction Safety**: Batch operations use transactions where appropriate

## Testing

To test the logging functionality:

1. Open `utils/test_import_logs.html` in your browser
2. Perform an import operation through the normal inventory import interface
3. Use the log viewer to verify that:
   - Import operation is logged in `logs_import`
   - Each row is logged in `logs_audit` with correct status
   - Statistics are accurate

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check that the database tables exist and have correct permissions
2. **Authentication errors**: Ensure user is logged in when viewing logs
3. **Missing import logs**: Check error logs for database connection issues

### Error Logging

The system logs errors to PHP error log when logging operations fail. Check your PHP error log for messages like:
- "Failed to prepare import log statement"
- "Failed to execute audit log statement"
- "Exception in logImportStart"

## Future Enhancements

Potential improvements to the logging system:
- Add log retention policies
- Implement log archiving
- Add more detailed performance metrics
- Create automated log analysis reports
- Add email notifications for failed imports
