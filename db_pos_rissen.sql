-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Oct 05, 2025 at 10:48 AM
-- Server version: 10.4.16-MariaDB
-- PHP Version: 7.4.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `db_pos_rissen`
--

-- --------------------------------------------------------

--
-- Table structure for table `category`
--

CREATE TABLE `category` (
  `id` int(11) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `category` text DEFAULT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `category`
--

INSERT INTO `category` (`id`, `store_id`, `category`, `description`) VALUES
(1, 1, 'category test', 'teest d'),
(2, 2, 'Electronics', 'Electronic devices and accessories'),
(3, NULL, 'Clothing', 'Apparel and fashion items'),
(4, NULL, 'Food & Beverages', 'Food items and drinks'),
(5, NULL, 'Books', 'Books and educational materials'),
(6, NULL, 'Home & Garden', 'Home improvement and garden supplies'),
(7, NULL, 'Sports', 'Sports equipment and accessories'),
(8, NULL, 'Toys', 'Toys and games'),
(9, NULL, 'Health & Beauty', 'Health and beauty products'),
(10, NULL, 'Automotive', 'Car parts and accessories'),
(11, NULL, 'Office Supplies', 'Office and business supplies'),
(12, 2, 'h', 'jn'),
(13, 2, 'nb', '');

-- --------------------------------------------------------

--
-- Table structure for table `logs_audit`
--

CREATE TABLE `logs_audit` (
  `id` int(11) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `log_type` text DEFAULT NULL,
  `page_title` text DEFAULT NULL,
  `column_barcode` text DEFAULT NULL,
  `column_item` text DEFAULT NULL,
  `column_qty` text DEFAULT NULL,
  `log_user` text DEFAULT NULL,
  `log_datetime` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `logs_import`
--

CREATE TABLE `logs_import` (
  `id` int(11) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `log_type` text DEFAULT NULL,
  `page_title` text DEFAULT NULL,
  `file_name` text DEFAULT NULL,
  `log_status` text DEFAULT NULL,
  `log_user` text DEFAULT NULL,
  `log_datetime` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `store_info`
--

CREATE TABLE `store_info` (
  `id` int(11) NOT NULL,
  `store_owner` int(11) DEFAULT NULL,
  `client` text NOT NULL,
  `client_description` text DEFAULT NULL,
  `client_location` text DEFAULT NULL,
  `client_logo` varchar(100) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `store_info`
--

INSERT INTO `store_info` (`id`, `store_owner`, `client`, `client_description`, `client_location`, `client_logo`, `created_at`, `updated_at`) VALUES
(1, 1, 'tins 1', 'test 1', 'test1', NULL, '2025-09-22 11:37:17', '2025-09-22 18:37:17'),
(2, 1, 'sj', 'jbk', 'bjb', NULL, '2025-09-22 11:38:00', '2025-09-22 18:38:00'),
(3, 1, 'nj', 'ml', 'j', NULL, '2025-09-23 06:58:43', '2025-09-23 13:58:43');

-- --------------------------------------------------------

--
-- Table structure for table `store_products`
--

CREATE TABLE `store_products` (
  `id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `product_name` text NOT NULL,
  `product_description` text DEFAULT NULL,
  `product_code` text DEFAULT NULL,
  `product_image` text DEFAULT NULL,
  `selling_price` decimal(11,0) DEFAULT NULL,
  `capital_price` decimal(10,0) DEFAULT NULL,
  `product_qty` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `store_products`
--

INSERT INTO `store_products` (`id`, `store_id`, `category_id`, `product_name`, `product_description`, `product_code`, `product_image`, `selling_price`, `capital_price`, `product_qty`, `created_at`, `updated_at`) VALUES
(3, 1, 3, 'Mil0 sachet 22g', 'Milo powder', '4800361394161', '4800361394161.jpg', '15', NULL, NULL, '2025-09-25 00:00:00', '2025-09-25 14:48:51'),
(4, 1, 1, 'Mil0 sachet 22g', 'Milo powder 2', '4800361394161', '4800361394161.jpg', '15', NULL, NULL, '2025-10-01 08:13:53', '2025-10-01 15:13:53'),
(5, 2, 1, 'Mil0 sachet 22g', 'Milo powder 3', '4800361394161', '4800361394161.jpg', '150', NULL, 20, '2025-10-01 08:15:37', '2025-10-05 06:01:33'),
(6, 1, 1, 'Mil0 sachet 22g', 'Milo powder 5', '4800361394161', '4800361394161.jpg', '15', NULL, NULL, '2025-10-01 08:31:19', '2025-10-01 15:31:19'),
(7, 1, 1, 'Mil0 sachet 22g', 'Milo powder 6', '4800361394161', '4800361394161.jpg', '151', NULL, NULL, '2025-10-01 08:34:46', '2025-10-01 15:34:46'),
(8, 1, 1, 'Mil0 sachet 22g', 'Milo powder', '4800361394161', '4800361394161.jpg', '152', NULL, NULL, '2025-10-01 09:06:30', '2025-10-01 16:06:30'),
(9, 1, 1, 'Mil0 sachet 22g', 'Milo powder tr', '4800361394161', '4800361394161.jpg', '15', NULL, NULL, '2025-10-01 09:07:33', '2025-10-01 16:07:33'),
(10, 1, 1, 'Mil0 sachet 22g', 'Milo powder er', '4800361394161', '4800361394161.jpg', '15', NULL, NULL, '2025-10-01 09:08:01', '2025-10-01 16:08:01'),
(11, 1, 1, 'Mil0 sachet 22g', 'Milo powder', '4800361394161', 'product_68dd53a446afb0.51450413.jpg', '15', NULL, NULL, '2025-10-01 09:15:32', '2025-10-01 16:15:32'),
(12, 1, 1, 'philippines', 'Milo powder everyday', '4800361394161', 'product_68dd53d6d5a856.09735806.png', '150', NULL, NULL, '2025-10-01 09:16:22', '2025-10-01 16:16:22'),
(13, 1, 1, 'tokwa', 'Milo powder', '4800361394161', 'product_68dd59549ee354.74499784.jpg', '153', NULL, NULL, '2025-10-01 09:39:48', '2025-10-01 16:39:48'),
(14, 1, 1, 'fairydail', 'Milo powder', '4800361394161', 'product_68dd5b5db3d7f3.54018136.png', '153', NULL, NULL, '2025-10-01 09:48:29', '2025-10-01 16:48:29'),
(15, 2, 2, 'Mil0 sachet 22g', 'Milo powder e', '4800361394161', '4800361394161.jpg', '15', NULL, NULL, '2025-10-01 10:35:19', '2025-10-01 17:35:19'),
(16, 2, 13, 'jh', 'jb', '8675', NULL, '654', NULL, 17, '2025-10-04 09:52:49', '2025-10-05 06:01:33'),
(17, 2, 2, 'sda', '', '443252', NULL, '2', NULL, 11, '2025-10-04 09:58:12', '2025-10-05 06:01:33');

-- --------------------------------------------------------

--
-- Table structure for table `sys_permissions`
--

CREATE TABLE `sys_permissions` (
  `id` int(11) NOT NULL,
  `permission_name` varchar(100) DEFAULT NULL,
  `permissions` varchar(5) DEFAULT NULL,
  `page_access` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `sys_user`
--

CREATE TABLE `sys_user` (
  `uid` int(11) NOT NULL,
  `fname` varchar(50) DEFAULT NULL,
  `mname` varchar(50) NOT NULL,
  `lname` varchar(50) DEFAULT NULL,
  `username` text DEFAULT NULL,
  `email` text DEFAULT NULL,
  `password` text NOT NULL,
  `profile` text NOT NULL,
  `role` set('Staff','Admin','Super') NOT NULL,
  `email_verify` set('Yes') DEFAULT NULL,
  `remember_token` text DEFAULT NULL,
  `status` set('Active','Inactive','Disabled') DEFAULT NULL,
  `permission_id` int(11) DEFAULT NULL,
  `sys_store_owner` set('Yes','No') NOT NULL,
  `sys_store_assigned` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `sys_user`
--

INSERT INTO `sys_user` (`uid`, `fname`, `mname`, `lname`, `username`, `email`, `password`, `profile`, `role`, `email_verify`, `remember_token`, `status`, `permission_id`, `sys_store_owner`, `sys_store_assigned`) VALUES
(1, 'renz', '', 'aq', 'renz', '<EMAIL>', '123', '', 'Super', NULL, NULL, 'Active', NULL, '', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `sys_user_logs`
--

CREATE TABLE `sys_user_logs` (
  `log_id` int(11) NOT NULL,
  `uid` int(11) NOT NULL,
  `type` text NOT NULL,
  `log_date` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `sys_user_logs`
--

INSERT INTO `sys_user_logs` (`log_id`, `uid`, `type`, `log_date`) VALUES
(1, 1, 'login', '2025-09-17 07:11:58'),
(2, 1, 'login', '2025-09-17 07:15:11'),
(3, 1, 'login', '2025-09-17 07:15:17'),
(4, 1, 'logout', '2025-09-17 07:15:25'),
(5, 1, 'login', '2025-09-17 07:15:36'),
(6, 1, 'login', '2025-09-17 07:23:15'),
(7, 1, 'logout', '2025-09-17 07:25:22'),
(8, 1, 'login', '2025-09-17 07:26:57'),
(9, 1, 'logout', '2025-09-17 07:30:31'),
(10, 1, 'login', '2025-09-17 07:30:39'),
(11, 1, 'login', '2025-09-17 07:34:33'),
(12, 1, 'logout', '2025-09-17 07:37:29'),
(13, 1, 'login', '2025-09-17 07:37:45'),
(14, 1, 'logout', '2025-09-17 07:38:23'),
(15, 1, 'login', '2025-09-17 07:38:32'),
(16, 1, 'login', '2025-09-22 18:37:04'),
(17, 1, 'login', '2025-09-23 13:08:18'),
(18, 1, 'login', '2025-09-24 13:02:51'),
(19, 1, 'logout', '2025-09-24 13:28:50'),
(20, 1, 'login', '2025-09-24 15:49:42'),
(21, 1, 'login', '2025-09-24 16:21:38'),
(22, 1, 'login', '2025-09-25 14:02:26'),
(23, 1, 'logout', '2025-09-25 18:48:53'),
(24, 1, 'login', '2025-09-25 18:49:01'),
(25, 1, 'login', '2025-09-27 06:12:05'),
(26, 1, 'login', '2025-09-27 15:42:21'),
(27, 1, 'login', '2025-09-27 16:14:38'),
(28, 1, 'login', '2025-09-27 16:31:25'),
(29, 1, 'login', '2025-09-27 16:32:36'),
(30, 1, 'login', '2025-09-28 07:23:48'),
(31, 1, 'login', '2025-09-28 16:23:58'),
(32, 1, 'login', '2025-09-28 17:57:55'),
(33, 1, 'login', '2025-09-28 17:58:57'),
(34, 1, 'login', '2025-09-28 19:08:13'),
(35, 1, 'login', '2025-09-28 19:56:24'),
(36, 1, 'login', '2025-09-28 20:01:51'),
(37, 1, 'login', '2025-10-01 14:06:13'),
(38, 1, 'logout', '2025-10-01 17:03:43'),
(39, 1, 'login', '2025-10-01 17:03:47'),
(40, 1, 'logout', '2025-10-01 17:06:22'),
(41, 1, 'login', '2025-10-01 17:06:25'),
(42, 1, 'login', '2025-10-03 17:22:05'),
(43, 1, 'logout', '2025-10-04 19:55:59'),
(44, 1, 'login', '2025-10-04 19:59:36'),
(45, 1, 'logout', '2025-10-04 20:02:26'),
(46, 1, 'login', '2025-10-04 20:02:33'),
(47, 1, 'login', '2025-10-05 04:24:41'),
(48, 1, 'logout', '2025-10-05 05:14:52'),
(49, 1, 'login', '2025-10-05 05:14:55'),
(50, 1, 'login', '2025-10-05 05:54:53'),
(51, 1, 'login', '2025-10-05 06:00:31'),
(52, 1, 'login', '2025-10-05 06:36:05');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `category`
--
ALTER TABLE `category`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `logs_audit`
--
ALTER TABLE `logs_audit`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `logs_import`
--
ALTER TABLE `logs_import`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `store_info`
--
ALTER TABLE `store_info`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `store_products`
--
ALTER TABLE `store_products`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sys_permissions`
--
ALTER TABLE `sys_permissions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sys_user`
--
ALTER TABLE `sys_user`
  ADD PRIMARY KEY (`uid`);

--
-- Indexes for table `sys_user_logs`
--
ALTER TABLE `sys_user_logs`
  ADD PRIMARY KEY (`log_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `category`
--
ALTER TABLE `category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `logs_audit`
--
ALTER TABLE `logs_audit`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `logs_import`
--
ALTER TABLE `logs_import`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `store_info`
--
ALTER TABLE `store_info`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `store_products`
--
ALTER TABLE `store_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `sys_permissions`
--
ALTER TABLE `sys_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sys_user`
--
ALTER TABLE `sys_user`
  MODIFY `uid` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `sys_user_logs`
--
ALTER TABLE `sys_user_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
